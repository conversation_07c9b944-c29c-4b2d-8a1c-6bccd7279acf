"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.darBajaHistorialToma = exports.actualizarHistorialToma = exports.obtenerHistorialToma = exports.listarHistorialToma = exports.insertarHistorialToma = void 0;
const appdatasource_1 = require("../config/appdatasource");
const historial_toma_1 = require("../entities/historial_toma");
const estado_auditoria_1 = require("../enums/estado-auditoria");
const repository = appdatasource_1.AppDataSource.getRepository(historial_toma_1.HistorialToma);
const insertarHistorialToma = (data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.save(data);
});
exports.insertarHistorialToma = insertarHistorialToma;
const listarHistorialToma = () => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.find({ where: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO, recordatorio: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } },
        relations: ['recordatorio']
    });
});
exports.listarHistorialToma = listarHistorialToma;
const obtenerHistorialToma = (idHistorialToma) => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.findOne({ where: { idHistorialToma, estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO, recordatorio: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } },
        relations: ['recordatorio']
    });
});
exports.obtenerHistorialToma = obtenerHistorialToma;
const actualizarHistorialToma = (idHistorialToma, data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idHistorialToma, data);
});
exports.actualizarHistorialToma = actualizarHistorialToma;
const darBajaHistorialToma = (idHistorialToma) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idHistorialToma, { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.INACTIVO });
});
exports.darBajaHistorialToma = darBajaHistorialToma;
//# sourceMappingURL=historial_toma.service.js.map