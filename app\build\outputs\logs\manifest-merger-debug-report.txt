-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:2:1-61:12
INJECTED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:2:1-61:12
INJECTED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:2:1-61:12
INJECTED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:2:1-61:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a8066e321064d4c7c1493ac740dddc0\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb9362af2063fe42d0e21d30e4a68405\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7598eece66a29e89626fe5b1aadfb5a7\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80fbf2f35cc5b46d18d8a3863b365436\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c955858a555a75f07fcaab7d50e7c463\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcbd90c863f624d9cf5b3f1deaf74017\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\872491339002a2f17b323f0543d92901\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d41f69d0bd719b29d79bddc2eae6411a\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99e758f468542c391e69eb3a43a6079f\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923660cd24b9d94e08bf24a5f3e6c782\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0ba4d1820f6871ec1881677187d2868\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46b06a6a2b12471feb332933385317aa\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\863ba001e1d987d1591358b94f62ee3a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\beec0037a3a99e0776441b7c4f5346cb\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c788560493f517bc95cef0387facae02\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02465d468f6e832bb7a4c9f8a4109307\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e0a7e4b31565f1ba42eda7bbcf592e7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8119271b37b8d1fdf6a6a8b48d0a5a1b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c82f3756b493b2543f4891c36b92a68\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e2de2e7cd49cbdc500578a871d324ce\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\791caa29688a95c622348b84d037fb53\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\baf55ef3fb341a63a1a2a5234d7c63b8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c929343a1c5dea1f0bdf38dc69547100\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5d5d8bf3cc3ef7a1db349bcdc9d8f42\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdb410dcfffea7d86bd185fbe9a73abf\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85e6b2015746443a324b271e5eb5b2ba\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd02d1e1bb30fb86ec6ea4977eb54a8c\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\679382b28202c41e1f82c2e2af645553\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04341cdcd8e82ec4bd6ffb76df290daa\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02a40e31c52251f918051f60ee84e19a\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7de44751cb12220c37ec71af48272ffe\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\baa1c2569f9daf19208582075a85aaf5\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1eb3494a86d7e3f14b35502b1cfa9840\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ff43ef43fd958eeaefa6a3537399243\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50fe8cbc1deba9e545e6e906c83f864f\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bc38270feecdf004b27240cb40b5866\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e7fbad89090ef929ecb8d45969e7262\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\390b1e2de36861edd5ee53d95fe76621\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea495a30cd14aef7de1513e9661565bc\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d8c30ce1a5e7c0598f04c340b748c11\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
	package
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:4:5-32
		INJECTED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:7:22-76
application
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:9:5-59:19
INJECTED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:9:5-59:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a8066e321064d4c7c1493ac740dddc0\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a8066e321064d4c7c1493ac740dddc0\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb9362af2063fe42d0e21d30e4a68405\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb9362af2063fe42d0e21d30e4a68405\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923660cd24b9d94e08bf24a5f3e6c782\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923660cd24b9d94e08bf24a5f3e6c782\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e0a7e4b31565f1ba42eda7bbcf592e7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e0a7e4b31565f1ba42eda7bbcf592e7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7de44751cb12220c37ec71af48272ffe\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7de44751cb12220c37ec71af48272ffe\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ff43ef43fd958eeaefa6a3537399243\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ff43ef43fd958eeaefa6a3537399243\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:16:9-35
	android:label
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:14:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:12:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:15:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:19:9-29
	android:icon
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:13:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:10:9-35
	android:theme
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:17:9-44
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:11:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:18:9-44
activity#com.grupo3.medrem.activities.SettingsActivity
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:20:9-22:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:22:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:21:13-56
activity#com.grupo3.medrem.activities.TermsActivity
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:23:9-25:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:25:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:24:13-53
activity#com.grupo3.medrem.activities.NewReminderActivity
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:26:9-28:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:28:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:27:13-59
activity#com.grupo3.medrem.activities.DashboardActivity
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:29:9-31:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:31:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:30:13-57
activity#com.grupo3.medrem.activities.RegisterActivity
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:34:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:33:13-56
activity#com.grupo3.medrem.activities.LoginActivity
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:35:9-37:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:37:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:36:13-53
activity#com.grupo3.medrem.activities.Onboarding4Activity
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:38:9-40:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:40:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:39:13-59
activity#com.grupo3.medrem.activities.Onboarding3Activity
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:41:9-43:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:43:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:42:13-59
activity#com.grupo3.medrem.activities.Onboarding2Activity
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:44:9-46:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:46:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:45:13-59
activity#com.grupo3.medrem.activities.SplashActivity
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:47:9-55:20
	android:exported
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:49:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:48:13-54
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:50:13-54:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:51:17-69
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:51:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:53:17-77
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:53:27-74
activity#com.grupo3.medrem.activities.MainActivity
ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:56:9-58:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:58:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml:57:13-52
uses-sdk
INJECTED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a8066e321064d4c7c1493ac740dddc0\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a8066e321064d4c7c1493ac740dddc0\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb9362af2063fe42d0e21d30e4a68405\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb9362af2063fe42d0e21d30e4a68405\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7598eece66a29e89626fe5b1aadfb5a7\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7598eece66a29e89626fe5b1aadfb5a7\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80fbf2f35cc5b46d18d8a3863b365436\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80fbf2f35cc5b46d18d8a3863b365436\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c955858a555a75f07fcaab7d50e7c463\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c955858a555a75f07fcaab7d50e7c463\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcbd90c863f624d9cf5b3f1deaf74017\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcbd90c863f624d9cf5b3f1deaf74017\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\872491339002a2f17b323f0543d92901\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\872491339002a2f17b323f0543d92901\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d41f69d0bd719b29d79bddc2eae6411a\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d41f69d0bd719b29d79bddc2eae6411a\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99e758f468542c391e69eb3a43a6079f\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99e758f468542c391e69eb3a43a6079f\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923660cd24b9d94e08bf24a5f3e6c782\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923660cd24b9d94e08bf24a5f3e6c782\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0ba4d1820f6871ec1881677187d2868\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0ba4d1820f6871ec1881677187d2868\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46b06a6a2b12471feb332933385317aa\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46b06a6a2b12471feb332933385317aa\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\863ba001e1d987d1591358b94f62ee3a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\863ba001e1d987d1591358b94f62ee3a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\beec0037a3a99e0776441b7c4f5346cb\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\beec0037a3a99e0776441b7c4f5346cb\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c788560493f517bc95cef0387facae02\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c788560493f517bc95cef0387facae02\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02465d468f6e832bb7a4c9f8a4109307\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02465d468f6e832bb7a4c9f8a4109307\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e0a7e4b31565f1ba42eda7bbcf592e7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e0a7e4b31565f1ba42eda7bbcf592e7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8119271b37b8d1fdf6a6a8b48d0a5a1b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8119271b37b8d1fdf6a6a8b48d0a5a1b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c82f3756b493b2543f4891c36b92a68\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c82f3756b493b2543f4891c36b92a68\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e2de2e7cd49cbdc500578a871d324ce\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e2de2e7cd49cbdc500578a871d324ce\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\791caa29688a95c622348b84d037fb53\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\791caa29688a95c622348b84d037fb53\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\baf55ef3fb341a63a1a2a5234d7c63b8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\baf55ef3fb341a63a1a2a5234d7c63b8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c929343a1c5dea1f0bdf38dc69547100\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c929343a1c5dea1f0bdf38dc69547100\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5d5d8bf3cc3ef7a1db349bcdc9d8f42\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5d5d8bf3cc3ef7a1db349bcdc9d8f42\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdb410dcfffea7d86bd185fbe9a73abf\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdb410dcfffea7d86bd185fbe9a73abf\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85e6b2015746443a324b271e5eb5b2ba\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85e6b2015746443a324b271e5eb5b2ba\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd02d1e1bb30fb86ec6ea4977eb54a8c\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd02d1e1bb30fb86ec6ea4977eb54a8c\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\679382b28202c41e1f82c2e2af645553\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\679382b28202c41e1f82c2e2af645553\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04341cdcd8e82ec4bd6ffb76df290daa\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04341cdcd8e82ec4bd6ffb76df290daa\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02a40e31c52251f918051f60ee84e19a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02a40e31c52251f918051f60ee84e19a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7de44751cb12220c37ec71af48272ffe\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7de44751cb12220c37ec71af48272ffe\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\baa1c2569f9daf19208582075a85aaf5\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\baa1c2569f9daf19208582075a85aaf5\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1eb3494a86d7e3f14b35502b1cfa9840\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1eb3494a86d7e3f14b35502b1cfa9840\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ff43ef43fd958eeaefa6a3537399243\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ff43ef43fd958eeaefa6a3537399243\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50fe8cbc1deba9e545e6e906c83f864f\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50fe8cbc1deba9e545e6e906c83f864f\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bc38270feecdf004b27240cb40b5866\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bc38270feecdf004b27240cb40b5866\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e7fbad89090ef929ecb8d45969e7262\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e7fbad89090ef929ecb8d45969e7262\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\390b1e2de36861edd5ee53d95fe76621\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\390b1e2de36861edd5ee53d95fe76621\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea495a30cd14aef7de1513e9661565bc\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea495a30cd14aef7de1513e9661565bc\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d8c30ce1a5e7c0598f04c340b748c11\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d8c30ce1a5e7c0598f04c340b748c11\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\SISE\Aplicaciones Moviles\Examen Final\am-g3-medrem\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923660cd24b9d94e08bf24a5f3e6c782\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e0a7e4b31565f1ba42eda7bbcf592e7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e0a7e4b31565f1ba42eda7bbcf592e7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7de44751cb12220c37ec71af48272ffe\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7de44751cb12220c37ec71af48272ffe\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923660cd24b9d94e08bf24a5f3e6c782\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923660cd24b9d94e08bf24a5f3e6c782\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923660cd24b9d94e08bf24a5f3e6c782\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923660cd24b9d94e08bf24a5f3e6c782\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923660cd24b9d94e08bf24a5f3e6c782\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923660cd24b9d94e08bf24a5f3e6c782\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923660cd24b9d94e08bf24a5f3e6c782\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e0a7e4b31565f1ba42eda7bbcf592e7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e0a7e4b31565f1ba42eda7bbcf592e7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e0a7e4b31565f1ba42eda7bbcf592e7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.grupo3.medrem.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.grupo3.medrem.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02c2b92f6e85d5b1a44e815ee286efca\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f553fc43164d66af47b29c12a752875\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
