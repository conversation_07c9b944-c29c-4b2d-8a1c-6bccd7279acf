"use strict";
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DB_DATABASE = exports.DB_PASSWORD = exports.DB_USERNAME = exports.DB_PORT = exports.DB_HOST = exports.DB_TYPE = exports.MensajeController = void 0;
require("dotenv/config");
var MensajeController;
(function (MensajeController) {
    MensajeController["INSERTADO_OK"] = "Insertado correctamente";
    MensajeController["ACTUALIZADO_OK"] = "Actualizado correctamente";
    MensajeController["ELIMINADO_OK"] = "Eliminado correctamente";
    MensajeController["NO_ENCONTRADO"] = "No encontrado";
    MensajeController["LOGIN_OK"] = "Login exitoso";
    MensajeController["LOGIN_ERROR"] = "Credenciales incorrectas";
})(MensajeController || (exports.MensajeController = MensajeController = {}));
_a = process.env, exports.DB_TYPE = _a.DB_TYPE, exports.DB_HOST = _a.DB_HOST, exports.DB_PORT = _a.DB_PORT, exports.DB_USERNAME = _a.DB_USERNAME, exports.DB_PASSWORD = _a.DB_PASSWORD, exports.DB_DATABASE = _a.DB_DATABASE;
//# sourceMappingURL=constants.js.map