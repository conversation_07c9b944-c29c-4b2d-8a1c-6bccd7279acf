"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loginUsuario = exports.darBajaUsuario = exports.actualizarUsuario = exports.obtenerUsuario = exports.listarUsuarios = exports.insertarUsuario = void 0;
const bcrypt_1 = __importDefault(require("bcrypt"));
const appdatasource_1 = require("../config/appdatasource");
const usuario_1 = require("../entities/usuario");
const estado_auditoria_1 = require("../enums/estado-auditoria");
const repository = appdatasource_1.AppDataSource.getRepository(usuario_1.Usuario);
const insertarUsuario = (data) => __awaiter(void 0, void 0, void 0, function* () {
    if (data.password) {
        const salt = yield bcrypt_1.default.genSalt(10);
        data.password = yield bcrypt_1.default.hash(data.password, salt);
    }
    const newUsuario = yield repository.save(data);
    return yield repository.findOne({ where: { idUsuario: newUsuario.idUsuario } });
});
exports.insertarUsuario = insertarUsuario;
const listarUsuarios = () => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.find({ where: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } });
});
exports.listarUsuarios = listarUsuarios;
const obtenerUsuario = (idUsuario) => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.findOne({ where: { idUsuario, estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } });
});
exports.obtenerUsuario = obtenerUsuario;
const actualizarUsuario = (idUsuario, data) => __awaiter(void 0, void 0, void 0, function* () {
    if (data.password) {
        const salt = yield bcrypt_1.default.genSalt(10);
        data.password = yield bcrypt_1.default.hash(data.password, salt);
    }
    yield repository.update(idUsuario, data);
});
exports.actualizarUsuario = actualizarUsuario;
const darBajaUsuario = (idUsuario) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idUsuario, { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.INACTIVO });
});
exports.darBajaUsuario = darBajaUsuario;
const loginUsuario = (correo, password) => __awaiter(void 0, void 0, void 0, function* () {
    const usuario = yield repository.findOneBy({ correo, estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO });
    if (!usuario) {
        return null;
    }
    const esValido = yield bcrypt_1.default.compare(password, usuario.password);
    if (!esValido) {
        return null;
    }
    return usuario;
});
exports.loginUsuario = loginUsuario;
//# sourceMappingURL=usuario.service.js.map