"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactoEmergencia = void 0;
const typeorm_1 = require("typeorm");
const usuario_1 = require("./usuario");
let ContactoEmergencia = class ContactoEmergencia {
};
exports.ContactoEmergencia = ContactoEmergencia;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'id_contacto_emergencia' }),
    __metadata("design:type", Number)
], ContactoEmergencia.prototype, "idContactoEmergencia", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => usuario_1.Usuario, (usuarios) => usuarios.usuarios_ce),
    (0, typeorm_1.JoinColumn)({ name: 'id_usuario' }),
    __metadata("design:type", usuario_1.Usuario)
], ContactoEmergencia.prototype, "usuario", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'nombre' }),
    __metadata("design:type", String)
], ContactoEmergencia.prototype, "nombre", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'apellido_paterno' }),
    __metadata("design:type", String)
], ContactoEmergencia.prototype, "apellidoPaterno", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'apellido_materno' }),
    __metadata("design:type", String)
], ContactoEmergencia.prototype, "apellidoMaterno", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'correo' }),
    __metadata("design:type", String)
], ContactoEmergencia.prototype, "correo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'telefono' }),
    __metadata("design:type", String)
], ContactoEmergencia.prototype, "telefono", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'estado_auditoria' }),
    __metadata("design:type", Number)
], ContactoEmergencia.prototype, "estadoAuditoria", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fecha_creacion' }),
    __metadata("design:type", Date)
], ContactoEmergencia.prototype, "fechaCreacion", void 0);
exports.ContactoEmergencia = ContactoEmergencia = __decorate([
    (0, typeorm_1.Entity)('contactos_emergencia')
], ContactoEmergencia);
//# sourceMappingURL=contacto_emergencia.js.map