"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.darBajaPresentacionMedicamento = exports.actualizarPresentacionMedicamento = exports.obtenerPresentacionMedicamento = exports.listarPresentacionMedicamento = exports.insertarPresentacionMedicamento = void 0;
const appdatasource_1 = require("../config/appdatasource");
const presentacion_medicamento_1 = require("../entities/presentacion_medicamento");
const estado_auditoria_1 = require("../enums/estado-auditoria");
const repository = appdatasource_1.AppDataSource.getRepository(presentacion_medicamento_1.PresentacionMedicamento);
const insertarPresentacionMedicamento = (data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.save(data);
});
exports.insertarPresentacionMedicamento = insertarPresentacionMedicamento;
const listarPresentacionMedicamento = () => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.find({ where: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } });
});
exports.listarPresentacionMedicamento = listarPresentacionMedicamento;
const obtenerPresentacionMedicamento = (idPresentacionMedicamento) => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.findOne({ where: { idPresentacionMedicamento, estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } });
});
exports.obtenerPresentacionMedicamento = obtenerPresentacionMedicamento;
const actualizarPresentacionMedicamento = (idPresentacionMedicamento, data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idPresentacionMedicamento, data);
});
exports.actualizarPresentacionMedicamento = actualizarPresentacionMedicamento;
const darBajaPresentacionMedicamento = (idPresentacionMedicamento) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idPresentacionMedicamento, { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.INACTIVO });
});
exports.darBajaPresentacionMedicamento = darBajaPresentacionMedicamento;
//# sourceMappingURL=presentacion_medicamento.service.js.map