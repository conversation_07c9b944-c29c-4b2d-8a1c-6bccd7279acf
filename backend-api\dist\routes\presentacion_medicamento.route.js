"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const presentacion_medicamento_controller_1 = require("../controllers/presentacion_medicamento.controller");
const router = (0, express_1.Router)();
router.post('/', presentacion_medicamento_controller_1.insertarPresentacionMedicamento);
router.get("/", presentacion_medicamento_controller_1.listarPresentacionMedicamento);
router.get('/:idPresentacionMedicamento', presentacion_medicamento_controller_1.obtenerPresentacionMedicamento);
router.put('/:idPresentacionMedicamento', presentacion_medicamento_controller_1.actualizarPresentacionMedicamento);
router.delete('/:idPresentacionMedicamento', presentacion_medicamento_controller_1.darBajaPresentacionMedicamento);
exports.default = router;
//# sourceMappingURL=presentacion_medicamento.route.js.map