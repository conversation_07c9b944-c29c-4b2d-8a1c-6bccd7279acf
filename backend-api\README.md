# RECORDATORIO_MED_API

## Backend: Aplicación Recordatorio de Medicamentos

### Descripción:
Este proyecto es el backend de una API diseñado para gestionar recordatorios de medicamentos, permitiendo a los usuarios registrar, organizar y recibir notificaciones sobre la toma de sus medicinas.

### Configuración y Ejecución:

1. **Clonar el repositorio**:
     ```bash
     git clone https://github.com/pr1vi3t/RECORDATORIO_MED_API
     cd RECORDATORIO_MED_API
     ```

2. **Configurar y preparar la base de datos**:
     * Ejecutar el script SQL `script-database.sql` (ubicado en la carpeta `database`) para crear la base de datos completa y necesaria para el proyecto.

3. **Instalar las dependencias del proyecto**:
     ```bash
     npm install
     ```

4. **Iniciar el proyecto**:
     ```bash
     npm start
     ```

### Documentación POSTMAN:
Para facilitar el uso y prueba de los endpoints disponibles, se ha documentado la API utilizando Postman. En esta colección encontrarás los endpoints principales, junto con ejemplos de peticiones y respuestas.

👉 Mira la documentación aquí:
[Ver documentación en Postman](https://documenter.getpostman.com/view/36779961/2sB2x5JDJd)

