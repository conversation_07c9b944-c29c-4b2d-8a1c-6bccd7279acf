"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Medicamento = void 0;
const typeorm_1 = require("typeorm");
const tipo_medicamento_1 = require("./tipo_medicamento");
const presentacion_medicamento_1 = require("./presentacion_medicamento");
const unidad_dosis_1 = require("./unidad_dosis");
const recordatorio_1 = require("./recordatorio");
let Medicamento = class Medicamento {
};
exports.Medicamento = Medicamento;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'id_medicamento' }),
    __metadata("design:type", Number)
], Medicamento.prototype, "idMedicamento", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'nombre' }),
    __metadata("design:type", String)
], Medicamento.prototype, "nombre", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'imagen_url' }),
    __metadata("design:type", String)
], Medicamento.prototype, "imagenUrl", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => tipo_medicamento_1.TipoMedicamento, (tipos_medicamento) => tipos_medicamento.tiposMedicamento),
    (0, typeorm_1.JoinColumn)({ name: 'id_tipo_medicamento' }),
    __metadata("design:type", tipo_medicamento_1.TipoMedicamento)
], Medicamento.prototype, "tipoMedicamento", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => presentacion_medicamento_1.PresentacionMedicamento, (presentaciones_medicamento) => presentaciones_medicamento.presentacionesMedicamento),
    (0, typeorm_1.JoinColumn)({ name: 'id_presentacion_medicamento' }),
    __metadata("design:type", presentacion_medicamento_1.PresentacionMedicamento)
], Medicamento.prototype, "presentacionMedicamento", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => unidad_dosis_1.UnidadDosis, (unidades_dosis) => unidades_dosis.unidadesDosis),
    (0, typeorm_1.JoinColumn)({ name: 'id_unidad_dosis' }),
    __metadata("design:type", unidad_dosis_1.UnidadDosis)
], Medicamento.prototype, "unidadDosis", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'dosis_cantidad' }),
    __metadata("design:type", Number)
], Medicamento.prototype, "dosis_cantidad", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'estado_auditoria' }),
    __metadata("design:type", Number)
], Medicamento.prototype, "estadoAuditoria", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'fecha_creacion' }),
    __metadata("design:type", Date)
], Medicamento.prototype, "fechaCreacion", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => recordatorio_1.Recordatorio, (recordatorio) => recordatorio.medicamento),
    __metadata("design:type", Array)
], Medicamento.prototype, "medicamentos", void 0);
exports.Medicamento = Medicamento = __decorate([
    (0, typeorm_1.Entity)('medicamentos')
], Medicamento);
//# sourceMappingURL=medicamento.js.map