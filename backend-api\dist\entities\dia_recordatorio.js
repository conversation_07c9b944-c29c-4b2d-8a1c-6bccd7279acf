"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiaRecordatorio = void 0;
const typeorm_1 = require("typeorm");
const recordatorio_1 = require("./recordatorio");
let DiaRecordatorio = class DiaRecordatorio {
};
exports.DiaRecordatorio = DiaRecordatorio;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'id_dia_recordatorio' }),
    __metadata("design:type", Number)
], DiaRecordatorio.prototype, "idDiaRecordatorio", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => recordatorio_1.Recordatorio, (recordatorios) => recordatorios.recordatorios),
    (0, typeorm_1.JoinColumn)({ name: 'id_recordatorio' }),
    __metadata("design:type", recordatorio_1.Recordatorio)
], DiaRecordatorio.prototype, "recordatorio", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'dia' }),
    __metadata("design:type", Number)
], DiaRecordatorio.prototype, "dia", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'estado_auditoria' }),
    __metadata("design:type", Number)
], DiaRecordatorio.prototype, "estadoAuditoria", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fecha_creacion' }),
    __metadata("design:type", Date)
], DiaRecordatorio.prototype, "fechaCreacion", void 0);
exports.DiaRecordatorio = DiaRecordatorio = __decorate([
    (0, typeorm_1.Entity)('dias_recordatorio')
], DiaRecordatorio);
//# sourceMappingURL=dia_recordatorio.js.map