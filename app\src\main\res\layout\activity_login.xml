<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activities.LoginActivity">


    <ImageView
        android:id="@+id/login_imageView"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="@dimen/margin_login_grande"
        android:contentDescription="TODO"
        android:src="@drawable/ic_mediremind_logo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/login_titulo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/login_titulo"
        android:textSize="@dimen/texto_titulo_login"
        android:textColor="@color/black"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/login_imageView" />

    <TextView
        android:id="@+id/login_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:text="@string/login_subtitulo"
        android:textSize="@dimen/texto_boton"
        android:textColor="@color/gris"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/login_titulo" />


    <EditText
        android:id="@+id/login_correo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="36dp"
        android:layout_marginEnd="36dp"
        android:autofillHints=""
        android:ems="10"
        android:hint="@string/login_correo"
        android:inputType="textEmailAddress"
        android:textSize="@dimen/texto_normal"
        android:layout_marginTop="@dimen/margin_onboarding"
        android:background="@drawable/login_label_style"
        android:padding="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/login_subtitle" />

    <EditText
        android:id="@+id/login_password"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginStart="36dp"
        android:layout_marginEnd="36dp"
        android:hint="@string/login_password"
        android:inputType="textPassword"
        android:textSize="@dimen/texto_normal"
        android:background="@drawable/login_label_style"
        android:padding="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/login_correo" />

    <Button
        android:id="@+id/login_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="36dp"
        android:layout_marginEnd="36dp"
        android:layout_marginTop="24dp"
        android:background="@drawable/button_style"
        android:text="@string/login_boton_login"
        android:textSize="@dimen/texto_boton"
        android:onClick="onLoginClick"
        app:backgroundTint="@null"
        app:layout_constraintTop_toBottomOf="@id/login_recordar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ProgressBar
        android:id="@+id/login_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/login_button"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp" />

    <CheckBox
        android:id="@+id/login_recordar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="36dp"
        android:layout_marginTop="16dp"
        android:text="@string/login_check"
        android:textSize="@dimen/texto_normal"
        android:textColor="@color/gris"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/login_password" />

    <TextView
        android:id="@+id/login_recordar_password"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="36dp"
        android:layout_marginTop="30dp"
        android:clickable="true"
        android:focusable="true"
        android:text="@string/login_recordarPassword"
        android:textColor="@color/blue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/login_recordar"
        app:layout_constraintTop_toBottomOf="@+id/login_password" />

    <View
        android:id="@+id/login_line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/gris"
        android:layout_marginTop="20dp"
        android:layout_marginStart="36dp"
        android:layout_marginEnd="36dp"
        app:layout_constraintTop_toBottomOf="@+id/login_button"/>

    <TextView
        android:id="@+id/login_cuenta_nueva"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="70dp"
        android:layout_marginTop="16dp"
        android:text="@string/login_cuenta_nueva"
        android:textColor="@color/gris"
        android:textSize="@dimen/texto_normal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/login_line" />

    <TextView
        android:id="@+id/login_button_registrar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="70dp"
        android:onClick="onclickRegister"
        android:text="@string/login_registrar"
        android:textColor="@color/blue"
        android:textSize="@dimen/texto_normal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/login_line" />

</androidx.constraintlayout.widget.ConstraintLayout>