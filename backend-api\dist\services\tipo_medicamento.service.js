"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.darBajaTipoMedicamento = exports.actualizarTipoMedicamento = exports.obtenerTipoMedicamento = exports.listarTipoMedicamento = exports.insertarTipoMedicamento = void 0;
const appdatasource_1 = require("../config/appdatasource");
const tipo_medicamento_1 = require("../entities/tipo_medicamento");
const estado_auditoria_1 = require("../enums/estado-auditoria");
const repository = appdatasource_1.AppDataSource.getRepository(tipo_medicamento_1.TipoMedicamento);
const insertarTipoMedicamento = (data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.save(data);
});
exports.insertarTipoMedicamento = insertarTipoMedicamento;
const listarTipoMedicamento = () => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.find({ where: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } });
});
exports.listarTipoMedicamento = listarTipoMedicamento;
const obtenerTipoMedicamento = (idTipoMedicamento) => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.findOne({ where: { idTipoMedicamento, estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } });
});
exports.obtenerTipoMedicamento = obtenerTipoMedicamento;
const actualizarTipoMedicamento = (idTipoMedicamento, data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idTipoMedicamento, data);
});
exports.actualizarTipoMedicamento = actualizarTipoMedicamento;
const darBajaTipoMedicamento = (idTipoMedicamento) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idTipoMedicamento, { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.INACTIVO });
});
exports.darBajaTipoMedicamento = darBajaTipoMedicamento;
//# sourceMappingURL=tipo_medicamento.service.js.map