"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.darBajaContactoEmergencia = exports.actualizarContactoEmergencia = exports.obtenerContactoEmergencia = exports.listarContactoEmergencia = exports.insertarContactoEmergencia = void 0;
const ContactoEmergenciaService = __importStar(require("../services/contacto_emergencia.service"));
const base_response_1 = require("../shared/base-response");
const constants_1 = require("../shared/constants");
const insertarContactoEmergencia = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const ContactoEmergencia = req.body;
        yield ContactoEmergenciaService.insertarContactoEmergencia(ContactoEmergencia);
        res.json(base_response_1.BaseResponse.success(null, constants_1.MensajeController.INSERTADO_OK));
    }
    catch (error) {
        console.error(error);
        res.status(500).json(base_response_1.BaseResponse.error(error.message));
    }
});
exports.insertarContactoEmergencia = insertarContactoEmergencia;
const listarContactoEmergencia = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const ContactoEmergencias = yield ContactoEmergenciaService.listarContactoEmergencia();
        res.json(base_response_1.BaseResponse.success(ContactoEmergencias));
    }
    catch (error) {
        console.error(error);
        res.status(500).json(base_response_1.BaseResponse.error(error.message));
    }
});
exports.listarContactoEmergencia = listarContactoEmergencia;
const obtenerContactoEmergencia = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { idContactoEmergencia } = req.params;
        const ContactoEmergencia = yield ContactoEmergenciaService.obtenerContactoEmergencia(Number(idContactoEmergencia));
        if (!ContactoEmergencia) {
            res.status(404).json(base_response_1.BaseResponse.error(constants_1.MensajeController.NO_ENCONTRADO, 404));
            return;
        }
        res.json(base_response_1.BaseResponse.success(ContactoEmergencia));
    }
    catch (error) {
        console.error(error);
        res.status(500).json(base_response_1.BaseResponse.error(error.message));
    }
});
exports.obtenerContactoEmergencia = obtenerContactoEmergencia;
const actualizarContactoEmergencia = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { idContactoEmergencia } = req.params;
        const ContactoEmergencia = req.body;
        if (!(yield ContactoEmergenciaService.obtenerContactoEmergencia(Number(idContactoEmergencia)))) {
            res.status(404).json(base_response_1.BaseResponse.error(constants_1.MensajeController.NO_ENCONTRADO, 404));
            return;
        }
        yield ContactoEmergenciaService.actualizarContactoEmergencia(Number(idContactoEmergencia), ContactoEmergencia);
        res.json(base_response_1.BaseResponse.success(constants_1.MensajeController.ACTUALIZADO_OK));
    }
    catch (error) {
        console.error(error);
        res.status(500).json(base_response_1.BaseResponse.error(error.message));
    }
});
exports.actualizarContactoEmergencia = actualizarContactoEmergencia;
const darBajaContactoEmergencia = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { idContactoEmergencia } = req.params;
        if (!(yield ContactoEmergenciaService.obtenerContactoEmergencia(Number(idContactoEmergencia)))) {
            res.status(404).json(base_response_1.BaseResponse.error(constants_1.MensajeController.NO_ENCONTRADO, 404));
            return;
        }
        yield ContactoEmergenciaService.darBajaContactoEmergencia(Number(idContactoEmergencia));
        res.json(base_response_1.BaseResponse.success(constants_1.MensajeController.ELIMINADO_OK));
    }
    catch (error) {
        console.error(error);
        res.status(400).json(base_response_1.BaseResponse.error(error.message));
    }
});
exports.darBajaContactoEmergencia = darBajaContactoEmergencia;
//# sourceMappingURL=contacto_emergencia.controller.js.map