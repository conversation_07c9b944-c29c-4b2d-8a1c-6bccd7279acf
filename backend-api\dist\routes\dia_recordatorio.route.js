"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const dia_recordatorio_controller_1 = require("../controllers/dia_recordatorio.controller");
const router = (0, express_1.Router)();
router.post('/', dia_recordatorio_controller_1.insertarDiaRecordatorio);
router.get("/", dia_recordatorio_controller_1.listarDiasRecordatorio);
router.get('/:idDiaRecordatorio', dia_recordatorio_controller_1.obtenerDiaRecordatorio);
router.put('/:idDiaRecordatorio', dia_recordatorio_controller_1.actualizarDiaRecordatorio);
router.delete('/:idDiaRecordatorio', dia_recordatorio_controller_1.darBajaDiaRecordatorio);
exports.default = router;
//# sourceMappingURL=dia_recordatorio.route.js.map