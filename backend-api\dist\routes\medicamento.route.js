"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const medicamento_controller_1 = require("../controllers/medicamento.controller");
const router = (0, express_1.Router)();
router.post('/', medicamento_controller_1.insertarMedicamento);
router.get("/", medicamento_controller_1.listarMedicamento);
router.get('/:idMedicamento', medicamento_controller_1.obtenerMedicamento);
router.put('/:idMedicamento', medicamento_controller_1.actualizarMedicamento);
router.delete('/:idMedicamento', medicamento_controller_1.darBajaMedicamento);
exports.default = router;
//# sourceMappingURL=medicamento.route.js.map