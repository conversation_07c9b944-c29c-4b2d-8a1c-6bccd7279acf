"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const tipo_medicamento_controller_1 = require("../controllers/tipo_medicamento.controller");
const router = (0, express_1.Router)();
router.post('/', tipo_medicamento_controller_1.insertarTipoMedicamento);
router.get("/", tipo_medicamento_controller_1.listarTipoMedicamento);
router.get('/:idTipoMedicamento', tipo_medicamento_controller_1.obtenerTipoMedicamento);
router.put('/:idTipoMedicamento', tipo_medicamento_controller_1.actualizarTipoMedicamento);
router.delete('/:idTipoMedicamento', tipo_medicamento_controller_1.darBajaTipoMedicamento);
exports.default = router;
//# sourceMappingURL=tipo_medicamento.route.js.map