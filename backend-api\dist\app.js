"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.startServer = void 0;
const express_1 = __importDefault(require("express"));
const appdatasource_1 = require("./config/appdatasource");
const usuario_route_1 = __importDefault(require("./routes/usuario.route"));
const tipo_medicamento_route_1 = __importDefault(require("./routes/tipo_medicamento.route"));
const unidad_dosis_route_1 = __importDefault(require("./routes/unidad_dosis.route"));
const presentacion_medicamento_route_1 = __importDefault(require("./routes/presentacion_medicamento.route"));
const medicamento_route_1 = __importDefault(require("./routes/medicamento.route"));
const recordatorio_route_1 = __importDefault(require("./routes/recordatorio.route"));
const dia_recordatorio_route_1 = __importDefault(require("./routes/dia_recordatorio.route"));
const frecuencia_route_1 = __importDefault(require("./routes/frecuencia.route"));
const contacto_emergencia_route_1 = __importDefault(require("./routes/contacto_emergencia.route"));
const historial_toma_route_1 = __importDefault(require("./routes/historial_toma.route"));
const app = (0, express_1.default)();
app.use(express_1.default.json());
app.use('/api/v1/usuarios', usuario_route_1.default);
app.use('/api/v1/tipos-medicamento', tipo_medicamento_route_1.default);
app.use('/api/v1/unidades-dosis', unidad_dosis_route_1.default);
app.use('/api/v1/presentaciones-medicamento', presentacion_medicamento_route_1.default);
app.use('/api/v1/medicamentos', medicamento_route_1.default);
app.use('/api/v1/recordatorios', recordatorio_route_1.default);
app.use('/api/v1/dias-recordatorio', dia_recordatorio_route_1.default);
app.use('/api/v1/frecuencias', frecuencia_route_1.default);
app.use('/api/v1/contactos-emergencia', contacto_emergencia_route_1.default);
app.use('/api/v1/historial-tomas', historial_toma_route_1.default);
const startServer = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield appdatasource_1.AppDataSource.initialize();
        console.log('La base de datos se ha conectado correctamente');
    }
    catch (error) {
        console.error('Error al conectar con la base de datos', error);
    }
});
exports.startServer = startServer;
exports.default = app;
//# sourceMappingURL=app.js.map