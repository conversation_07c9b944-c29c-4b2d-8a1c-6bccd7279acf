"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const contacto_emergencia_controller_1 = require("../controllers/contacto_emergencia.controller");
const router = (0, express_1.Router)();
router.post('/', contacto_emergencia_controller_1.insertarContactoEmergencia);
router.get("/", contacto_emergencia_controller_1.listarContactoEmergencia);
router.get('/:idContactoEmergencia', contacto_emergencia_controller_1.obtenerContactoEmergencia);
router.put('/:idContactoEmergencia', contacto_emergencia_controller_1.actualizarContactoEmergencia);
router.delete('/:idContactoEmergencia', contacto_emergencia_controller_1.darBajaContactoEmergencia);
exports.default = router;
//# sourceMappingURL=contacto_emergencia.route.js.map