"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnidadDosis = void 0;
const typeorm_1 = require("typeorm");
const medicamento_1 = require("./medicamento");
let UnidadDosis = class UnidadDosis {
};
exports.UnidadDosis = UnidadDosis;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'id_unidad_dosis' }),
    __metadata("design:type", Number)
], UnidadDosis.prototype, "idUnidadDosis", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'nombre' }),
    __metadata("design:type", String)
], UnidadDosis.prototype, "nombre", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'abreviatura' }),
    __metadata("design:type", String)
], UnidadDosis.prototype, "abreviatura", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'estado_auditoria' }),
    __metadata("design:type", Number)
], UnidadDosis.prototype, "estadoAuditoria", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fecha_creacion' }),
    __metadata("design:type", Date)
], UnidadDosis.prototype, "fechaCreacion", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => medicamento_1.Medicamento, (medicamento) => medicamento.unidadDosis),
    __metadata("design:type", Array)
], UnidadDosis.prototype, "unidadesDosis", void 0);
exports.UnidadDosis = UnidadDosis = __decorate([
    (0, typeorm_1.Entity)('unidades_dosis')
], UnidadDosis);
//# sourceMappingURL=unidad_dosis.js.map