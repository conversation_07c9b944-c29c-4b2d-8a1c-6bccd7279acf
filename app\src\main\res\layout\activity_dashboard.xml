<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/topPanel"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/top_panel_background"
        app:layout_constraintHeight_percent="0.1"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/appNameText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="@string/main_titulo"
            android:textColor="#2962FF"
            android:textSize="24sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/profileIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_profile"
            android:contentDescription="@string/dashboard_ic_profile_contentDescription"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/clockIcon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/clockIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_clock"
            android:contentDescription="@string/dashboard_ic_clock_contentDescription"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/heartIcon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/heartIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_heart"
            android:contentDescription="@string/dashboard_ic_heart_contentDescription"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/notificationIcon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/notificationIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_notification"
            android:contentDescription="@string/dashboard_ic_notification_contentDescription"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/middleContent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintHeight_percent="0.8"
        app:layout_constraintTop_toBottomOf="@id/topPanel"
        app:layout_constraintBottom_toTopOf="@id/bottomPanel">

        <LinearLayout
            android:id="@+id/todayRemindersSection"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            android:background="#F5F5F5"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/todayTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp"
                android:text="@string/dashboard_today_title"
                android:textSize="20sp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/todayRemindersList"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingBottom="8dp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/futureRemindersSection"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            android:background="#F5F5F5"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintTop_toBottomOf="@id/todayRemindersSection"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:id="@+id/futureTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp"
                android:text="@string/dashboard_future_title"
                android:textSize="20sp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/futureRemindersList"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingBottom="8dp" />
        </LinearLayout>

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/addReminderFab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:contentDescription="@string/dashboard_add_reminder_contentDescription"
            android:src="@drawable/ic_add"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bottomPanel"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/bottom_panel_background"
        app:layout_constraintHeight_percent="0.1"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:weightSum="4">

            <LinearLayout
                android:id="@+id/homeButton"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_home"
                    android:contentDescription="@string/dashboard_home" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/dashboard_home"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/historyButton"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_history"
                    android:contentDescription="@string/dashboard_history" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/dashboard_history"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/termsButton"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_terms"
                    android:contentDescription="@string/dashboard_termsContentDescription" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/dashboard_terms"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/settingsButton"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_settings"
                    android:contentDescription="@string/dashboard_settings" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/dashboard_settings"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>