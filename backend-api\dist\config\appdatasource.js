"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppDataSource = void 0;
const constants_1 = require("../shared/constants");
const typeorm_1 = require("typeorm");
const usuario_1 = require("../entities/usuario");
const tipo_medicamento_1 = require("../entities/tipo_medicamento");
const unidad_dosis_1 = require("../entities/unidad_dosis");
const presentacion_medicamento_1 = require("../entities/presentacion_medicamento");
const medicamento_1 = require("../entities/medicamento");
const recordatorio_1 = require("../entities/recordatorio");
const dia_recordatorio_1 = require("../entities/dia_recordatorio");
const frecuencia_1 = require("../entities/frecuencia");
const contacto_emergencia_1 = require("../entities/contacto_emergencia");
const historial_toma_1 = require("../entities/historial_toma");
exports.AppDataSource = new typeorm_1.DataSource({
    type: constants_1.DB_TYPE,
    host: constants_1.DB_HOST,
    port: Number(constants_1.DB_PORT || '0'),
    username: constants_1.DB_USERNAME,
    password: constants_1.DB_PASSWORD,
    database: constants_1.DB_DATABASE,
    entities: [usuario_1.Usuario, tipo_medicamento_1.TipoMedicamento, unidad_dosis_1.UnidadDosis, presentacion_medicamento_1.PresentacionMedicamento, medicamento_1.Medicamento, recordatorio_1.Recordatorio, dia_recordatorio_1.DiaRecordatorio, frecuencia_1.Frecuencia, contacto_emergencia_1.ContactoEmergencia, historial_toma_1.HistorialToma]
});
//# sourceMappingURL=appdatasource.js.map