"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const usuario_controller_1 = require("../controllers/usuario.controller");
const router = (0, express_1.Router)();
router.post('/', usuario_controller_1.insertarUsuario);
router.get("/", usuario_controller_1.listarUsuarios);
router.get('/:idUsuario', usuario_controller_1.obtenerUsuario);
router.put('/:idUsuario', usuario_controller_1.actualizarUsuario);
router.delete('/:idUsuario', usuario_controller_1.darBajaUsuario);
router.post("/login", usuario_controller_1.loginUsuario);
exports.default = router;
//# sourceMappingURL=usuario.route.js.map