"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TipoMedicamento = void 0;
const typeorm_1 = require("typeorm");
const medicamento_1 = require("./medicamento");
let TipoMedicamento = class TipoMedicamento {
};
exports.TipoMedicamento = TipoMedicamento;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'id_tipo_medicamento' }),
    __metadata("design:type", Number)
], TipoMedicamento.prototype, "idTipoMedicamento", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'nombre' }),
    __metadata("design:type", String)
], TipoMedicamento.prototype, "nombre", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'estado_auditoria' }),
    __metadata("design:type", Number)
], TipoMedicamento.prototype, "estadoAuditoria", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fecha_creacion' }),
    __metadata("design:type", Date)
], TipoMedicamento.prototype, "fechaCreacion", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => medicamento_1.Medicamento, (medicamento) => medicamento.tipoMedicamento),
    __metadata("design:type", Array)
], TipoMedicamento.prototype, "tiposMedicamento", void 0);
exports.TipoMedicamento = TipoMedicamento = __decorate([
    (0, typeorm_1.Entity)('tipos_medicamento')
], TipoMedicamento);
//# sourceMappingURL=tipo_medicamento.js.map