"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.darBajaFrecuencia = exports.actualizarFrecuencia = exports.obtenerFrecuencia = exports.listarFrecuencias = exports.insertarFrecuencia = void 0;
const appdatasource_1 = require("../config/appdatasource");
const frecuencia_1 = require("../entities/frecuencia");
const estado_auditoria_1 = require("../enums/estado-auditoria");
const repository = appdatasource_1.AppDataSource.getRepository(frecuencia_1.Frecuencia);
const insertarFrecuencia = (data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.save(data);
});
exports.insertarFrecuencia = insertarFrecuencia;
const listarFrecuencias = () => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.find({ where: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } });
});
exports.listarFrecuencias = listarFrecuencias;
const obtenerFrecuencia = (idFrecuencia) => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.findOne({ where: { idFrecuencia, estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } });
});
exports.obtenerFrecuencia = obtenerFrecuencia;
const actualizarFrecuencia = (idFrecuencia, data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idFrecuencia, data);
});
exports.actualizarFrecuencia = actualizarFrecuencia;
const darBajaFrecuencia = (idFrecuencia) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idFrecuencia, { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.INACTIVO });
});
exports.darBajaFrecuencia = darBajaFrecuencia;
//# sourceMappingURL=frecuencia.service.js.map