{"name": "recordatorio_med_api", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "start": "npm run build && node dist/server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"bcrypt": "^6.0.0", "dotenv": "^16.5.0", "express": "^5.1.0", "mysql2": "^3.14.1", "typeorm": "^0.3.24"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/express": "^5.0.2", "@types/node": "^22.15.29", "typescript": "^5.8.3"}}