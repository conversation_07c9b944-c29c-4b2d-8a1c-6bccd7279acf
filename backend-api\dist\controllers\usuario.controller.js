"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loginUsuario = exports.darBajaUsuario = exports.actualizarUsuario = exports.obtenerUsuario = exports.listarUsuarios = exports.insertarUsuario = void 0;
const usuarioService = __importStar(require("../services/usuario.service"));
const base_response_1 = require("../shared/base-response");
const constants_1 = require("../shared/constants");
const insertarUsuario = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const usuario = req.body;
        const newUsuario = yield usuarioService.insertarUsuario(usuario);
        res.json(base_response_1.BaseResponse.success(newUsuario, constants_1.MensajeController.INSERTADO_OK));
    }
    catch (error) {
        console.error(error);
        res.status(500).json(base_response_1.BaseResponse.error(error.message));
    }
});
exports.insertarUsuario = insertarUsuario;
const listarUsuarios = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const usuarios = yield usuarioService.listarUsuarios();
        res.json(base_response_1.BaseResponse.success(usuarios));
    }
    catch (error) {
        console.error(error);
        res.status(500).json(base_response_1.BaseResponse.error(error.message));
    }
});
exports.listarUsuarios = listarUsuarios;
const obtenerUsuario = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { idUsuario } = req.params;
        const usuario = yield usuarioService.obtenerUsuario(Number(idUsuario));
        if (!usuario) {
            res.status(404).json(base_response_1.BaseResponse.error(constants_1.MensajeController.NO_ENCONTRADO, 404));
            return;
        }
        res.json(base_response_1.BaseResponse.success(usuario));
    }
    catch (error) {
        console.error(error);
        res.status(500).json(base_response_1.BaseResponse.error(error.message));
    }
});
exports.obtenerUsuario = obtenerUsuario;
const actualizarUsuario = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { idUsuario } = req.params;
        const usuario = req.body;
        if (!(yield usuarioService.obtenerUsuario(Number(idUsuario)))) {
            res.status(404).json(base_response_1.BaseResponse.error(constants_1.MensajeController.NO_ENCONTRADO, 404));
            return;
        }
        yield usuarioService.actualizarUsuario(Number(idUsuario), usuario);
        res.json(base_response_1.BaseResponse.success(constants_1.MensajeController.ACTUALIZADO_OK));
    }
    catch (error) {
        console.error(error);
        res.status(500).json(base_response_1.BaseResponse.error(error.message));
    }
});
exports.actualizarUsuario = actualizarUsuario;
const darBajaUsuario = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { idUsuario } = req.params;
        if (!(yield usuarioService.obtenerUsuario(Number(idUsuario)))) {
            res.status(404).json(base_response_1.BaseResponse.error(constants_1.MensajeController.NO_ENCONTRADO, 404));
            return;
        }
        yield usuarioService.darBajaUsuario(Number(idUsuario));
        res.json(base_response_1.BaseResponse.success(constants_1.MensajeController.ELIMINADO_OK));
    }
    catch (error) {
        console.error(error);
        res.status(400).json(base_response_1.BaseResponse.error(error.message));
    }
});
exports.darBajaUsuario = darBajaUsuario;
const loginUsuario = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { correo, password } = req.body;
        const usuario = yield usuarioService.loginUsuario(correo, password);
        if (!usuario) {
            res.status(401).json(base_response_1.BaseResponse.error(constants_1.MensajeController.LOGIN_ERROR, 404));
            return;
        }
        const { password: _ } = usuario, usuarioSinPassword = __rest(usuario, ["password"]);
        res.json(base_response_1.BaseResponse.success(usuarioSinPassword, constants_1.MensajeController.LOGIN_OK));
    }
    catch (error) {
        console.error(error);
        res.status(500).json(base_response_1.BaseResponse.error(error.message));
    }
});
exports.loginUsuario = loginUsuario;
//# sourceMappingURL=usuario.controller.js.map