"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const historial_toma_controller_1 = require("../controllers/historial_toma.controller");
const router = (0, express_1.Router)();
router.post('/', historial_toma_controller_1.insertarHistorialToma);
router.get("/", historial_toma_controller_1.listarHistorialToma);
router.get('/:idHistorialToma', historial_toma_controller_1.obtenerHistorialToma);
router.put('/:idHistorialToma', historial_toma_controller_1.actualizarHistorialToma);
router.delete('/:idHistorialToma', historial_toma_controller_1.darBajaHistorialToma);
exports.default = router;
//# sourceMappingURL=historial_toma.route.js.map