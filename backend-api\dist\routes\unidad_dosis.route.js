"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const unidad_dosis_controller_1 = require("../controllers/unidad_dosis.controller");
const router = (0, express_1.Router)();
router.post('/', unidad_dosis_controller_1.insertarUnidadDosis);
router.get("/", unidad_dosis_controller_1.listarUnidadDosis);
router.get('/:idUnidadDosis', unidad_dosis_controller_1.obtenerUnidadDosis);
router.put('/:idUnidadDosis', unidad_dosis_controller_1.actualizarUnidadDosis);
router.delete('/:idUnidadDosis', unidad_dosis_controller_1.darBajaUnidadDosis);
exports.default = router;
//# sourceMappingURL=unidad_dosis.route.js.map