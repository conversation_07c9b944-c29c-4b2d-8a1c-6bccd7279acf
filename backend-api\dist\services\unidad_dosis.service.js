"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.darBajaUnidadDosis = exports.actualizarUnidadDosis = exports.obtenerUnidadDosis = exports.listarUnidadDosis = exports.insertarUnidadDosis = void 0;
const appdatasource_1 = require("../config/appdatasource");
const unidad_dosis_1 = require("../entities/unidad_dosis");
const estado_auditoria_1 = require("../enums/estado-auditoria");
const repository = appdatasource_1.AppDataSource.getRepository(unidad_dosis_1.UnidadDosis);
const insertarUnidadDosis = (data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.save(data);
});
exports.insertarUnidadDosis = insertarUnidadDosis;
const listarUnidadDosis = () => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.find({ where: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } });
});
exports.listarUnidadDosis = listarUnidadDosis;
const obtenerUnidadDosis = (idUnidadDosis) => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.findOne({ where: { idUnidadDosis, estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } });
});
exports.obtenerUnidadDosis = obtenerUnidadDosis;
const actualizarUnidadDosis = (idUnidadDosis, data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idUnidadDosis, data);
});
exports.actualizarUnidadDosis = actualizarUnidadDosis;
const darBajaUnidadDosis = (idUnidadDosis) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idUnidadDosis, { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.INACTIVO });
});
exports.darBajaUnidadDosis = darBajaUnidadDosis;
//# sourceMappingURL=unidad_dosis.service.js.map