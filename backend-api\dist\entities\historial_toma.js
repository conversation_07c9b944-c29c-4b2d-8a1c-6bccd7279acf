"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistorialToma = void 0;
const typeorm_1 = require("typeorm");
const recordatorio_1 = require("./recordatorio");
let HistorialToma = class HistorialToma {
};
exports.HistorialToma = HistorialToma;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'id_historial_toma' }),
    __metadata("design:type", Number)
], HistorialToma.prototype, "idHistorialToma", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => recordatorio_1.Recordatorio, (recordatorios) => recordatorios.recordatorios_ht),
    (0, typeorm_1.JoinColumn)({ name: 'id_recordatorio' }),
    __metadata("design:type", recordatorio_1.Recordatorio)
], HistorialToma.prototype, "recordatorio", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fecha' }),
    __metadata("design:type", Date)
], HistorialToma.prototype, "fecha", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'hora' }),
    __metadata("design:type", String)
], HistorialToma.prototype, "hora", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'estado' }),
    __metadata("design:type", String)
], HistorialToma.prototype, "estado", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'estado_auditoria' }),
    __metadata("design:type", Number)
], HistorialToma.prototype, "estadoAuditoria", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fecha_creacion' }),
    __metadata("design:type", Date)
], HistorialToma.prototype, "fechaCreacion", void 0);
exports.HistorialToma = HistorialToma = __decorate([
    (0, typeorm_1.Entity)('historial_tomas')
], HistorialToma);
//# sourceMappingURL=historial_toma.js.map