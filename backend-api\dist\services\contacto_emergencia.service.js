"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.darBajaContactoEmergencia = exports.actualizarContactoEmergencia = exports.obtenerContactoEmergencia = exports.listarContactoEmergencia = exports.insertarContactoEmergencia = void 0;
const appdatasource_1 = require("../config/appdatasource");
const contacto_emergencia_1 = require("../entities/contacto_emergencia");
const estado_auditoria_1 = require("../enums/estado-auditoria");
const repository = appdatasource_1.AppDataSource.getRepository(contacto_emergencia_1.ContactoEmergencia);
const insertarContactoEmergencia = (data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.save(data);
});
exports.insertarContactoEmergencia = insertarContactoEmergencia;
const listarContactoEmergencia = () => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.find({ where: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO, usuario: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } },
        relations: ['usuario']
    });
});
exports.listarContactoEmergencia = listarContactoEmergencia;
const obtenerContactoEmergencia = (idContactoEmergencia) => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.findOne({ where: { idContactoEmergencia, estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO, usuario: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } },
        relations: ['usuario']
    });
});
exports.obtenerContactoEmergencia = obtenerContactoEmergencia;
const actualizarContactoEmergencia = (idContactoEmergencia, data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idContactoEmergencia, data);
});
exports.actualizarContactoEmergencia = actualizarContactoEmergencia;
const darBajaContactoEmergencia = (idContactoEmergencia) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idContactoEmergencia, { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.INACTIVO });
});
exports.darBajaContactoEmergencia = darBajaContactoEmergencia;
//# sourceMappingURL=contacto_emergencia.service.js.map