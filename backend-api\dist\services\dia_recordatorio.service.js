"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.darBajaDiaRecordatorio = exports.actualizarDiaRecordatorio = exports.obtenerDiaRecordatorio = exports.listarDiasRecordatorio = exports.insertarDiaRecordatorio = void 0;
const appdatasource_1 = require("../config/appdatasource");
const dia_recordatorio_1 = require("../entities/dia_recordatorio");
const estado_auditoria_1 = require("../enums/estado-auditoria");
const repository = appdatasource_1.AppDataSource.getRepository(dia_recordatorio_1.DiaRecordatorio);
const insertarDiaRecordatorio = (data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.save(data);
});
exports.insertarDiaRecordatorio = insertarDiaRecordatorio;
const listarDiasRecordatorio = () => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.find({ where: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO,
            recordatorio: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } },
        relations: ['recordatorio', 'recordatorio.usuario', 'recordatorio.medicamento', 'recordatorio.frecuencia'] });
});
exports.listarDiasRecordatorio = listarDiasRecordatorio;
const obtenerDiaRecordatorio = (idDiaRecordatorio) => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.findOne({ where: { idDiaRecordatorio, estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO, recordatorio: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } },
        relations: ['recordatorio'] });
});
exports.obtenerDiaRecordatorio = obtenerDiaRecordatorio;
const actualizarDiaRecordatorio = (idDiaRecordatorio, data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idDiaRecordatorio, data);
});
exports.actualizarDiaRecordatorio = actualizarDiaRecordatorio;
const darBajaDiaRecordatorio = (idDiaRecordatorio) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idDiaRecordatorio, { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.INACTIVO });
});
exports.darBajaDiaRecordatorio = darBajaDiaRecordatorio;
//# sourceMappingURL=dia_recordatorio.service.js.map