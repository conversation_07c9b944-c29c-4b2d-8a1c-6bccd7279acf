plugins {
    alias(libs.plugins.android.application)
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')

if (localPropertiesFile.exists()) {
    localPropertiesFile.withInputStream { stream ->
        localProperties.load(stream)
    }
}

android {
    namespace 'com.grupo3.medrem'
    compileSdk 35
    buildFeatures {
        buildConfig = true
    }
    defaultConfig {
        applicationId "com.grupo3.medrem"
        buildConfigField "String", "BASE_URL", "\"${localProperties.getProperty('base_url', 'http://default.api.url/')}\""
        minSdk 24
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
    // Retrofit + Gson
    implementation libs.retrofit
    implementation libs.converter.gson
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")
}