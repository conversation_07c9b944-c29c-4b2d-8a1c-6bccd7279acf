package com.grupo3.medrem.activities;

import android.content.Intent;
import android.os.Bundle;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Switch;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.grupo3.medrem.R;

public class SettingsActivity extends AppCompatActivity {

    private ImageView backButton;
    private LinearLayout notificationsContainer;
    private LinearLayout darkModeContainer;
    private LinearLayout languageContainer;
    private LinearLayout privacyContainer;
    private LinearLayout profileContainer;
    private LinearLayout logoutContainer;
    private Switch notificationsSwitch;
    private Switch darkModeSwitch;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        initializeViews();
        setupClickListeners();
    }

    private void initializeViews() {
        backButton = findViewById(R.id.backButton);
        notificationsContainer = findViewById(R.id.notificationsContainer);
        darkModeContainer = findViewById(R.id.darkModeContainer);
        languageContainer = findViewById(R.id.languageContainer);
        privacyContainer = findViewById(R.id.privacyContainer);
        profileContainer = findViewById(R.id.profileContainer);
        logoutContainer = findViewById(R.id.logoutContainer);
        notificationsSwitch = findViewById(R.id.notificationsSwitch);
        darkModeSwitch = findViewById(R.id.darkModeSwitch);
    }

    private void setupClickListeners() {
        backButton.setOnClickListener(v -> finish());

        notificationsContainer.setOnClickListener(v -> {
            notificationsSwitch.setChecked(!notificationsSwitch.isChecked());
            handleNotificationsToggle(notificationsSwitch.isChecked());
        });

        darkModeContainer.setOnClickListener(v -> {
            darkModeSwitch.setChecked(!darkModeSwitch.isChecked());
            handleDarkModeToggle(darkModeSwitch.isChecked());
        });

        languageContainer.setOnClickListener(v -> {
            Toast.makeText(this, "Función de idioma próximamente", Toast.LENGTH_SHORT).show();
        });

        privacyContainer.setOnClickListener(v -> {
            Toast.makeText(this, "Configuración de privacidad próximamente", Toast.LENGTH_SHORT).show();
        });

        profileContainer.setOnClickListener(v -> {
            Toast.makeText(this, "Configuración de perfil próximamente", Toast.LENGTH_SHORT).show();
        });

        logoutContainer.setOnClickListener(v -> {
            handleLogout();
        });

        notificationsSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            handleNotificationsToggle(isChecked);
        });

        darkModeSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            handleDarkModeToggle(isChecked);
        });
    }

    private void handleNotificationsToggle(boolean isEnabled) {
        if (isEnabled) {
            Toast.makeText(this, "Notificaciones activadas", Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(this, "Notificaciones desactivadas", Toast.LENGTH_SHORT).show();
        }
    }

    private void handleDarkModeToggle(boolean isEnabled) {
        if (isEnabled) {
            Toast.makeText(this, "Modo oscuro activado", Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(this, "Modo oscuro desactivado", Toast.LENGTH_SHORT).show();
        }
    }

    private void handleLogout() {
        Toast.makeText(this, "Cerrando sesión...", Toast.LENGTH_SHORT).show();
        
        Intent intent = new Intent(this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }
}
