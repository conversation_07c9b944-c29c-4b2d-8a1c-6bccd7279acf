"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.darBajaMedicamento = exports.actualizarMedicamento = exports.obtenerMedicamento = exports.listarMedicamento = exports.insertarMedicamento = void 0;
const appdatasource_1 = require("../config/appdatasource");
const medicamento_1 = require("../entities/medicamento");
const estado_auditoria_1 = require("../enums/estado-auditoria");
const repository = appdatasource_1.AppDataSource.getRepository(medicamento_1.Medicamento);
const insertarMedicamento = (data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.save(data);
});
exports.insertarMedicamento = insertarMedicamento;
const listarMedicamento = () => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.find({ where: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO, tipoMedicamento: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO }, presentacionMedicamento: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO }, unidadDosis: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } },
        relations: ['tipoMedicamento', 'presentacionMedicamento', 'unidadDosis']
    });
});
exports.listarMedicamento = listarMedicamento;
const obtenerMedicamento = (idMedicamento) => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.findOne({ where: { idMedicamento, estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO, tipoMedicamento: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO }, presentacionMedicamento: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO }, unidadDosis: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } },
        relations: ['tipoMedicamento', 'presentacionMedicamento', 'unidadDosis']
    });
});
exports.obtenerMedicamento = obtenerMedicamento;
const actualizarMedicamento = (idMedicamento, data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idMedicamento, data);
});
exports.actualizarMedicamento = actualizarMedicamento;
const darBajaMedicamento = (idMedicamento) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idMedicamento, { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.INACTIVO });
});
exports.darBajaMedicamento = darBajaMedicamento;
//# sourceMappingURL=medicamento.service.js.map