"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Recordatorio = void 0;
const typeorm_1 = require("typeorm");
const usuario_1 = require("./usuario");
const medicamento_1 = require("./medicamento");
const frecuencia_1 = require("./frecuencia");
const dia_recordatorio_1 = require("./dia_recordatorio");
const historial_toma_1 = require("./historial_toma");
let Recordatorio = class Recordatorio {
};
exports.Recordatorio = Recordatorio;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'id_recordatorio' }),
    __metadata("design:type", Number)
], Recordatorio.prototype, "idRecordatorio", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => usuario_1.Usuario, (usuarios) => usuarios.usuarios),
    (0, typeorm_1.JoinColumn)({ name: 'id_usuario' }),
    __metadata("design:type", usuario_1.Usuario)
], Recordatorio.prototype, "usuario", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => medicamento_1.Medicamento, (medicamentos) => medicamentos.medicamentos),
    (0, typeorm_1.JoinColumn)({ name: 'id_medicamento' }),
    __metadata("design:type", medicamento_1.Medicamento)
], Recordatorio.prototype, "medicamento", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => frecuencia_1.Frecuencia, (frecuencias) => frecuencias.frecuencias),
    (0, typeorm_1.JoinColumn)({ name: 'id_frecuencia' }),
    __metadata("design:type", frecuencia_1.Frecuencia)
], Recordatorio.prototype, "frecuencia", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fecha_inicio' }),
    __metadata("design:type", Date)
], Recordatorio.prototype, "fechaInicio", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fecha_fin' }),
    __metadata("design:type", Date)
], Recordatorio.prototype, "fechaFin", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'hora' }),
    __metadata("design:type", String)
], Recordatorio.prototype, "hora", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'notas' }),
    __metadata("design:type", String)
], Recordatorio.prototype, "notas", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'estado_auditoria' }),
    __metadata("design:type", Number)
], Recordatorio.prototype, "estadoAuditoria", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fecha_creacion' }),
    __metadata("design:type", Date)
], Recordatorio.prototype, "fechaCreacion", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => dia_recordatorio_1.DiaRecordatorio, (diaRecordatorio) => diaRecordatorio.recordatorio),
    __metadata("design:type", Array)
], Recordatorio.prototype, "recordatorios", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => historial_toma_1.HistorialToma, (historialToma) => historialToma.recordatorio),
    __metadata("design:type", Array)
], Recordatorio.prototype, "recordatorios_ht", void 0);
exports.Recordatorio = Recordatorio = __decorate([
    (0, typeorm_1.Entity)('recordatorios')
], Recordatorio);
//# sourceMappingURL=recordatorio.js.map