<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activities.MainActivity">

    <TextView
        android:id="@+id/main_titulo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="200dp"
        android:text="@string/main_titulo"
        android:textColor="@color/blue"
        android:textSize="@dimen/texto_titulo"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/main_subtitulo"
        android:layout_width="250dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="60dp"
        android:text="@string/main_subtitulo"
        android:gravity="center"
        android:textColor="@color/gris"
        android:textSize="@dimen/texto_subtitulo"
        android:textStyle="italic"
        app:layout_constraintBottom_toTopOf="@+id/main_button_comenzar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_titulo" />

    <Button
        android:id="@+id/main_button_comenzar"
        android:layout_width="260dp"
        android:layout_height="wrap_content"
        android:background="@drawable/button_style"
        android:onClick="onClickOnboarding2"
        android:layout_marginTop="60dp"
        android:text="@string/main_button_comenzar"
        android:textSize="@dimen/texto_boton"
        android:textColor="@color/white"
        app:backgroundTint="@null"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_subtitulo" />

    <Button
        android:id="@+id/main_button_salir"
        android:layout_width="260dp"
        android:layout_height="wrap_content"
        android:background="@drawable/button_style2"
        android:onClick="onClickSalir"
        app:backgroundTint="@null"
        android:layout_marginTop="35dp"
        android:text="@string/main_button_salir"
        android:textSize="@dimen/texto_boton"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_button_comenzar" />

</androidx.constraintlayout.widget.ConstraintLayout>