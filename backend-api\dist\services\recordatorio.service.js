"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.darBajaRecordatorio = exports.actualizarRecordatorio = exports.obtenerRecordatorio = exports.listarRecordatorios = exports.insertarRecordatorio = void 0;
const appdatasource_1 = require("../config/appdatasource");
const recordatorio_1 = require("../entities/recordatorio");
const estado_auditoria_1 = require("../enums/estado-auditoria");
const repository = appdatasource_1.AppDataSource.getRepository(recordatorio_1.Recordatorio);
const insertarRecordatorio = (data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.save(data);
});
exports.insertarRecordatorio = insertarRecordatorio;
const listarRecordatorios = () => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.find({ where: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO,
            usuario: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO },
            medicamento: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO },
            frecuencia: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } },
        relations: ['usuario', 'medicamento', 'frecuencia'] });
});
exports.listarRecordatorios = listarRecordatorios;
const obtenerRecordatorio = (idRecordatorio) => __awaiter(void 0, void 0, void 0, function* () {
    return yield repository.findOne({ where: { idRecordatorio, estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO, usuario: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO }, medicamento: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO }, frecuencia: { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.ACTIVO } },
        relations: ['usuario', 'medicamento', 'frecuencia'] });
});
exports.obtenerRecordatorio = obtenerRecordatorio;
const actualizarRecordatorio = (idRecordatorio, data) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idRecordatorio, data);
});
exports.actualizarRecordatorio = actualizarRecordatorio;
const darBajaRecordatorio = (idRecordatorio) => __awaiter(void 0, void 0, void 0, function* () {
    yield repository.update(idRecordatorio, { estadoAuditoria: estado_auditoria_1.EstadoAuditoria.INACTIVO });
});
exports.darBajaRecordatorio = darBajaRecordatorio;
//# sourceMappingURL=recordatorio.service.js.map